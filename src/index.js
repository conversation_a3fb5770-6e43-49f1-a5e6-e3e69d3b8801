import React from "react";
import ReactDOM from "react-dom/client";
import $ from "jquery";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
// import { PersistGate } from "redux-persist/integration/react";
// import { Provider } from "react-redux";
// import App from "./App";
import Main from "./main";
import reportWebVitals from "./reportWebVitals";
import "./utils/i18n";
// import store, { Persistor } from "./redux/store";

window.jQuery = $;
require("bootstrap");

// Suppress ResizeObserver loop errors (common in video conferencing apps)
const resizeObserverErrorHandler = (e) => {
  if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {
    const resizeObserverErrDiv = document.getElementById('webpack-dev-server-client-overlay-div');
    const resizeObserverErr = document.getElementById('webpack-dev-server-client-overlay');
    if (resizeObserverErr) {
      resizeObserverErr.setAttribute('style', 'display: none');
    }
    if (resizeObserverErrDiv) {
      resizeObserverErrDiv.setAttribute('style', 'display: none');
    }
    e.stopImmediatePropagation();
    e.stopPropagation();
    return false;
  }
  return true;
};

// Add error event listener for ResizeObserver errors
window.addEventListener('error', resizeObserverErrorHandler);
window.addEventListener('unhandledrejection', (e) => {
  if (e.reason?.message?.includes('ResizeObserver loop completed with undelivered notifications')) {
    e.preventDefault();
    return false;
  }
});

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <React.StrictMode>
    <BrowserRouter>
      {/* <Provider store={store}>
        <PersistGate persistor={Persistor}>
          <App />
        </PersistGate>
      </Provider> */}
      <Main />
    </BrowserRouter>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
